<template>
  <q-dialog v-model="showModal" persistent>
    <q-card style="min-width: 800px; max-width: 90vw; max-height: 90vh">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">
          {{ isEditing ? 'Editar Evento' : 'Novo Evento' }}
        </div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section class="q-pt-none" style="max-height: 70vh; overflow-y: auto">
        <q-form @submit="onSubmit" class="q-gutter-md">
          <!-- Informações Básicas -->
          <div class="text-subtitle1 q-mt-md q-mb-sm">Informações do Evento</div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-8">
              <q-input
                v-model="form.title"
                label="Título *"
                outlined
                :rules="[(val) => !!val || 'Título é obrigatório']"
              />
            </div>

            <div class="col-12 col-md-4">
              <q-select
                v-model="form.type"
                :options="eventTypeOptions"
                label="Tipo *"
                outlined
                emit-value
                map-options
                :rules="[(val) => !!val || 'Tipo é obrigatório']"
              />
            </div>
          </div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-input
                v-model="form.startDate"
                label="Data e Hora de Início *"
                outlined
                type="datetime-local"
                :rules="[(val) => !!val || 'Data de início é obrigatória']"
              />
            </div>

            <div class="col-12 col-md-6">
              <q-input
                v-model="form.endDate"
                label="Data e Hora de Fim *"
                outlined
                type="datetime-local"
                :rules="[(val) => !!val || 'Data de fim é obrigatória']"
              />
            </div>
          </div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-select
                v-model="form.status"
                :options="eventStatusOptions"
                label="Status *"
                outlined
                emit-value
                map-options
                :rules="[(val) => !!val || 'Status é obrigatório']"
              />
            </div>

            <div class="col-12 col-md-6">
              <q-input v-model="form.location" label="Local" outlined />
            </div>
          </div>

          <q-input v-model="form.description" label="Descrição" outlined type="textarea" rows="3" />

          <!-- Paciente -->
          <div class="text-subtitle1 q-mt-lg q-mb-sm">Paciente</div>

          <q-select
            v-model="form.patientId"
            :options="patientOptions"
            label="Selecionar Paciente (Opcional)"
            outlined
            emit-value
            map-options
            clearable
            use-input
            input-debounce="300"
            @filter="filterPatients"
          >
            <template v-slot:no-option>
              <q-item>
                <q-item-section class="text-grey"> Nenhum paciente encontrado </q-item-section>
              </q-item>
            </template>
          </q-select>

          <!-- Convidados -->
          <div class="text-subtitle1 q-mt-lg q-mb-sm">Convidados</div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-8">
              <q-input
                v-model="newGuest"
                label="Adicionar convidado (email ou nome)"
                outlined
                @keyup.enter="addGuest"
              />
            </div>

            <div class="col-12 col-md-4">
              <q-btn
                label="Adicionar"
                color="primary"
                @click="addGuest"
                :disable="!newGuest.trim()"
              />
            </div>
          </div>

          <div v-if="form.guests && form.guests.length > 0" class="q-mt-sm">
            <q-chip
              v-for="(guest, index) in form.guests"
              :key="index"
              removable
              @remove="removeGuest(index)"
              color="primary"
              text-color="white"
              class="q-mr-sm q-mb-sm"
            >
              {{ guest }}
            </q-chip>
          </div>

          <!-- Recorrência -->
          <div class="text-subtitle1 q-mt-lg q-mb-sm">Repetição</div>

          <q-select
            v-model="recurrenceType"
            :options="recurrenceTypeOptions"
            label="Repetir evento"
            outlined
            emit-value
            map-options
            @update:model-value="onRecurrenceTypeChange"
          />

          <!-- Configurações de Recorrência -->
          <div v-if="recurrenceType !== 'none'" class="q-mt-md">
            <div class="row q-gutter-md">
              <div class="col-12 col-md-6">
                <q-input
                  v-model.number="recurrenceInterval"
                  label="Repetir a cada"
                  outlined
                  type="number"
                  min="1"
                  :suffix="getIntervalSuffix()"
                />
              </div>

              <div class="col-12 col-md-6">
                <q-select
                  v-model="recurrenceEndType"
                  :options="[
                    { value: 'never', label: 'Nunca' },
                    { value: 'date', label: 'Em uma data' },
                    { value: 'occurrences', label: 'Após X ocorrências' },
                  ]"
                  label="Terminar"
                  outlined
                  emit-value
                  map-options
                />
              </div>
            </div>

            <div v-if="recurrenceEndType === 'date'" class="q-mt-md">
              <q-input v-model="recurrenceEndDate" label="Data de término" outlined type="date" />
            </div>

            <div v-if="recurrenceEndType === 'occurrences'" class="q-mt-md">
              <q-input
                v-model.number="recurrenceOccurrences"
                label="Número de ocorrências"
                outlined
                type="number"
                min="1"
              />
            </div>

            <!-- Dias da semana para recorrência semanal -->
            <div v-if="recurrenceType === 'weekly'" class="q-mt-md">
              <div class="text-body2 q-mb-sm">Repetir nos dias:</div>
              <div class="row q-gutter-sm">
                <q-btn
                  v-for="day in daysOfWeekOptions"
                  :key="day.value"
                  :label="day.short"
                  :color="recurrenceDaysOfWeek.includes(day.value) ? 'primary' : 'grey-4'"
                  :text-color="recurrenceDaysOfWeek.includes(day.value) ? 'white' : 'grey-8'"
                  size="sm"
                  @click="toggleDayOfWeek(day.value)"
                />
              </div>
            </div>
          </div>

          <!-- Observações -->
          <div class="text-subtitle1 q-mt-lg q-mb-sm">Observações</div>

          <q-input
            v-model="form.notes"
            label="Observações adicionais"
            outlined
            type="textarea"
            rows="3"
          />
        </q-form>
      </q-card-section>

      <q-card-actions align="right" class="q-pa-md">
        <q-btn flat label="Cancelar" color="primary" v-close-popup />
        <q-btn label="Salvar" color="primary" @click="onSubmit" :loading="loading" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import type { EventFormData, Event, EventRecurrence } from 'src/models/event';
import {
  EventType,
  EventStatus,
  RecurrenceType,
  EVENT_STATUS_OPTIONS,
  RECURRENCE_TYPE_OPTIONS,
  DAYS_OF_WEEK_OPTIONS,
} from 'src/models/event';
import { useEventStore } from 'src/stores/event-store';
import { usePatientStore } from 'src/stores/patient-store';
import { useEventTypeStore } from 'src/stores/event-type-store';

interface Props {
  modelValue: boolean;
  event?: Event;
  initialDate?: Date;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'saved', event: Event): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const $q = useQuasar();
const eventStore = useEventStore();
const patientStore = usePatientStore();
const eventTypeStore = useEventTypeStore();

// State
const loading = ref(false);
const newGuest = ref('');

// Form data
const form = ref<EventFormData>({
  title: '',
  description: '',
  startDate: '',
  endDate: '',
  patientId: '',
  type: EventType.CONSULTATION,
  status: EventStatus.SCHEDULED,
  location: '',
  notes: '',
  guests: [],
});

// Recurrence state
const recurrenceType = ref<RecurrenceType>(RecurrenceType.NONE);
const recurrenceInterval = ref(1);
const recurrenceEndType = ref<'never' | 'date' | 'occurrences'>('never');
const recurrenceEndDate = ref('');
const recurrenceOccurrences = ref(1);
const recurrenceDaysOfWeek = ref<number[]>([]);

// Computed
const showModal = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const isEditing = computed(() => !!props.event?.id);

const eventTypeOptions = computed(() =>
  eventTypeStore.eventTypeOptions.map((option) => ({
    label: option.label,
    value: option.value,
  })),
);

const eventStatusOptions = computed(() =>
  EVENT_STATUS_OPTIONS.map((option) => ({
    label: option.label,
    value: option.value,
  })),
);

const recurrenceTypeOptions = computed(() =>
  RECURRENCE_TYPE_OPTIONS.map((option) => ({
    label: option.label,
    value: option.value,
  })),
);

const daysOfWeekOptions = computed(() => DAYS_OF_WEEK_OPTIONS);

const patientOptions = ref<Array<{ label: string; value: string }>>([]);

// Methods
const resetForm = () => {
  const now = new Date();
  const startDate = props.initialDate || now;
  const endDate = new Date(startDate.getTime() + 60 * 60 * 1000); // +1 hour

  const firstActiveType = eventTypeStore.activeEventTypes[0];

  form.value = {
    title: '',
    description: '',
    startDate: formatDateTimeLocal(startDate),
    endDate: formatDateTimeLocal(endDate),
    patientId: '',
    type: firstActiveType?.id || 'default-1',
    status: EventStatus.SCHEDULED,
    location: '',
    notes: '',
    guests: [],
  };

  recurrenceType.value = RecurrenceType.NONE;
  recurrenceInterval.value = 1;
  recurrenceEndType.value = 'never';
  recurrenceEndDate.value = '';
  recurrenceOccurrences.value = 1;
  recurrenceDaysOfWeek.value = [];
  newGuest.value = '';
};

const formatDateTimeLocal = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

const loadFormData = () => {
  if (props.event) {
    const event = props.event;
    form.value = {
      title: event.title,
      description: event.description || '',
      startDate: formatDateTimeLocal(new Date(event.startDate)),
      endDate: formatDateTimeLocal(new Date(event.endDate)),
      patientId: event.patientId || '',
      type: event.type,
      status: event.status,
      location: event.location || '',
      notes: event.notes || '',
      guests: event.guests ? [...event.guests] : [],
    };

    if (event.recurrence) {
      recurrenceType.value = event.recurrence.type;
      recurrenceInterval.value = event.recurrence.interval;

      if (event.recurrence.endDate) {
        recurrenceEndType.value = 'date';
        recurrenceEndDate.value = event.recurrence.endDate.split('T')[0];
      } else if (event.recurrence.occurrences) {
        recurrenceEndType.value = 'occurrences';
        recurrenceOccurrences.value = event.recurrence.occurrences;
      } else {
        recurrenceEndType.value = 'never';
      }

      if (event.recurrence.daysOfWeek) {
        recurrenceDaysOfWeek.value = [...event.recurrence.daysOfWeek];
      }
    }
  } else {
    resetForm();
  }
};

const filterPatients = (val: string, update: (fn: () => void) => void) => {
  update(() => {
    const patients = patientStore.activePatients;
    if (val === '') {
      patientOptions.value = patients.map((patient) => ({
        label: `${patient.name} ${patient.lastName}`,
        value: patient.id || '',
      }));
    } else {
      const needle = val.toLowerCase();
      patientOptions.value = patients
        .filter((patient) => `${patient.name} ${patient.lastName}`.toLowerCase().includes(needle))
        .map((patient) => ({
          label: `${patient.name} ${patient.lastName}`,
          value: patient.id || '',
        }));
    }
  });
};

const addGuest = () => {
  const guest = newGuest.value.trim();
  if (guest && !form.value.guests?.includes(guest)) {
    if (!form.value.guests) {
      form.value.guests = [];
    }
    form.value.guests.push(guest);
    newGuest.value = '';
  }
};

const removeGuest = (index: number) => {
  if (form.value.guests) {
    form.value.guests.splice(index, 1);
  }
};

const onRecurrenceTypeChange = (type: RecurrenceType) => {
  if (type === RecurrenceType.NONE) {
    form.value.recurrence = undefined;
  } else {
    // Set default day of week for weekly recurrence
    if (type === RecurrenceType.WEEKLY && recurrenceDaysOfWeek.value.length === 0) {
      const startDate = new Date(form.value.startDate);
      recurrenceDaysOfWeek.value = [startDate.getDay()];
    }
  }
};

const getIntervalSuffix = (): string => {
  switch (recurrenceType.value) {
    case RecurrenceType.DAILY:
      return recurrenceInterval.value === 1 ? 'dia' : 'dias';
    case RecurrenceType.WEEKLY:
      return recurrenceInterval.value === 1 ? 'semana' : 'semanas';
    case RecurrenceType.MONTHLY:
      return recurrenceInterval.value === 1 ? 'mês' : 'meses';
    case RecurrenceType.YEARLY:
      return recurrenceInterval.value === 1 ? 'ano' : 'anos';
    default:
      return '';
  }
};

const toggleDayOfWeek = (day: number) => {
  const index = recurrenceDaysOfWeek.value.indexOf(day);
  if (index > -1) {
    recurrenceDaysOfWeek.value.splice(index, 1);
  } else {
    recurrenceDaysOfWeek.value.push(day);
  }
};

const buildRecurrence = () => {
  if (recurrenceType.value === RecurrenceType.NONE) {
    return undefined;
  }

  const recurrence: EventRecurrence = {
    type: recurrenceType.value,
    interval: recurrenceInterval.value,
  };

  if (recurrenceEndType.value === 'date' && recurrenceEndDate.value) {
    recurrence.endDate = recurrenceEndDate.value + 'T23:59:59.999Z';
  } else if (recurrenceEndType.value === 'occurrences') {
    recurrence.occurrences = recurrenceOccurrences.value;
  }

  if (recurrenceType.value === RecurrenceType.WEEKLY && recurrenceDaysOfWeek.value.length > 0) {
    recurrence.daysOfWeek = [...recurrenceDaysOfWeek.value];
  }

  return recurrence;
};

const onSubmit = async () => {
  loading.value = true;

  try {
    const eventData: EventFormData = {
      ...form.value,
      startDate: new Date(form.value.startDate).toISOString(),
      endDate: new Date(form.value.endDate).toISOString(),
      recurrence: buildRecurrence(),
    };

    let savedEvent: Event;

    if (isEditing.value && props.event) {
      savedEvent = await eventStore.updateEvent(props.event.id, eventData);
    } else {
      savedEvent = await eventStore.createEvent(eventData);
    }

    $q.notify({
      type: 'positive',
      message: isEditing.value ? 'Evento atualizado com sucesso!' : 'Evento criado com sucesso!',
    });

    emit('saved', savedEvent);
    showModal.value = false;
  } catch (error: { message: string | undefined | null }) {
    $q.notify({
      type: 'negative',
      message:
        'Erro ao salvar evento. Tente novamente.' + `${error.message ? '\n' + error.message : ''}`,
    });
  } finally {
    loading.value = false;
  }
};

// Watchers
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      loadFormData();
      // Load patients for the select
      filterPatients('', (fn) => fn());
    }
  },
);
</script>

<style lang="scss" scoped>
.q-card {
  .q-card-section {
    &:first-child {
      border-bottom: 1px solid #e0e0e0;
    }
  }
}
</style>
